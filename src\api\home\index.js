import request from "@/utils/request";

// 待办事项（工单）
export function listDbsxgd() {
  return request({
    url: "/tyywpt/tygjzxgjlb/dbsxgd",
    method: "get",
  });
}

// 获取用户最大角色
export function getUserRoleMax() {
  return request({
    url: "/tyywpt/tygjzxgjlb/getUserRoleMax",
    method: "get",
  });
}

// 告警趋势
export function getGjqs() {
  return request({
    url: "/tyywpt/tygjzxgjlb/gjqs",
    method: "get",
  });
}

// 未完成告警
export function getWwcgj() {
  return request({
    url: "/tyywpt/tygjzxgjlb/wwcgj",
    method: "get",
  });
}

// 工单处置情况top5
export function getGdczqkTop5(query) {
  return request({
    url: "/tyywpt/tygjzxgjlb/gdczqkTop5",
    method: "get",
    params: query,
  });
}

// 待办工单
export function getDbList(query) {
  return request({
    url: "/tyywpt/tTyywGd/dbList",
    method: "get",
    params: query,
  });
}

// 云资源数量
export function getYzyData(query) {
  return request({
    url: "/tyywpt/tYyDxZytj/deviceTotal",
    method: "get",
    params: query,
  });
}

// 应用在线数
export function getonlineRate() {
  return request({
    url: "/tyywpt/tTyywYy/onlineRate",
    method: "get",
  });
}

// 水位
export function getdeviceRate(query) {
  return request({
    url: "/tyywpt/tYyDxZytj/deviceRate",
    method: "get",
    params: query,
  });
}