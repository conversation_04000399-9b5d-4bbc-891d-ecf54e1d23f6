<template>
  <el-dialog title="安全隐患详情" :visible="show" width="65%" @close="close">
    <div class="info">
      <div class="line">
        <div class="label">工单编号</div>
        <div class="value text_link" v-if="info.gdId" @click="goDetail">
          {{ info.gdNo }}
        </div>
        <div class="value" v-else>-</div>
      </div>
      <div class="line">
        <div class="label">编号</div>
        <div class="value">{{ info.serialId || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">关联部门</div>
        <div class="value">{{ info.deptName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">关联应用</div>
        <div class="value">{{ info.yyName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警等级</div>
        <div class="value">{{ info.priority || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警时间</div>
        <div class="value">{{ info.createdAtLower || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警状态</div>
        <div class="value">
          {{ info.status || "-" }}
        </div>
      </div>
      <div class="line">
        <div class="label">告警类型</div>
        <div class="value">
          {{ info.workOrderType == 1 ? "风险隐患" : "安全事件" }}
        </div>
      </div>
      <div class="line">
        <div class="label">来源</div>
        <div class="value">{{ info.sjType == 1 ? "告知" : "通知" }}</div>
      </div>
      <div class="line">
        <div class="label">事件或者隐患类型</div>
        <div class="value">{{ info.eventRiskType || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">发起单位</div>
        <div class="value">{{ info.initiatingUnit || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">对象</div>
        <div class="value">{{ info.securityObject || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">完成单位</div>
        <div class="value">{{ info.completionUnit || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">超时情况</div>
        <div class="value">{{ info.retentionStatus || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置方式</div>
        <div class="value">
          {{ info.czfs == 1 ? "忽略" : info.czfs == 2 ? "转工单" : "" }}
        </div>
      </div>
      <div class="line">
        <div class="label">标题</div>
        <div class="value">{{ info.notificationTitle || "-" }}</div>
      </div>

      <div class="line">
        <div class="label">更新时间</div>
        <div class="value">{{ info.updateAtLower || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">签收时间</div>
        <div class="value">{{ info.signForTime || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置时长</div>
        <div class="value">{{ info.disposalTime || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">截止时间</div>
        <div class="value">{{ info.overtimeEndAt || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">通知正文——隐患类型</div>
        <div class="value">{{ info.tbr || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">通知正文——附件</div>
        <div class="value">{{ info.notificationBody || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告知详情</div>
        <div class="value">{{ info.securityDetail || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">反馈要求</div>
        <div class="value">{{ info.callbackRequire || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处理记录</div>
        <div class="value">
          <div
            v-if="processingRecordsList.length > 0"
            class="processing-records"
          >
            <el-table
              :data="processingRecordsList"
              border
              size="small"
              style="width: 100%"
            >
              <el-table-column
                prop="name"
                label="任务名称"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="operationDetail"
                label="操作描述"
                min-width="150"
                show-overflow-tooltip
              />
              <!-- <el-table-column prop="type" label="类型" width="80" /> -->
              <el-table-column
                prop="assigneeUserName"
                label="经办人名称"
                width="100"
              />
              <el-table-column
                prop="assigneeCompanyName"
                label="经办人单位名称"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="assigneeRoleName"
                label="经办人角色名称"
                width="120"
              />
              <el-table-column prop="gmtCreate" label="创建时间" width="150" />
              <el-table-column prop="glaimTime" label="完成时间" width="150" />
              <el-table-column
                prop="approveResult"
                label="审批结果"
                width="100"
              />
              <el-table-column label="审批附件" min-width="150">
                <template slot-scope="scope">
                  <div
                    v-if="
                      scope.row.approveAnnex &&
                      scope.row.approveAnnex.length > 0
                    "
                    class="annex-buttons"
                  >
                    <el-button
                      v-for="(annex, index) in scope.row.approveAnnex"
                      :key="index"
                      type="text"
                      size="mini"
                      class="annex-btn"
                      @click="downloadFile(annex.url, annex.name)"
                    >
                      {{ annex.name || `附件${index + 1}` }}
                    </el-button>
                  </div>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="approveOpinion"
                label="审批意见备注"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column
                prop="reason"
                label="原因"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="accountabilityReport"
                label="追责报告"
                min-width="120"
                show-overflow-tooltip
              />
            </el-table>
          </div>
          <span v-else>-</span>
        </div>
      </div>
      <div class="line">
        <div class="label">举证信息</div>
        <div class="value">{{ info.evidenceInfo || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">数据来源</div>
        <div class="value">{{ info.dataSource || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">事件资产 IP</div>
        <div class="value">{{ info.assetIp || "-" }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  computed: {
    // 将处理记录字符串转换为表格数据数组
    processingRecordsList() {
      if (!this.info.processingRecords) {
        return [];
      }

      // 如果已经是数组，检查是否为对象数组
      if (Array.isArray(this.info.processingRecords)) {
        return this.info.processingRecords.map((record) =>
          this.normalizeRecord(record)
        );
      }

      // 如果是字符串，尝试解析JSON格式
      const records = this.info.processingRecords;
      try {
        const parsed = JSON.parse(records);
        if (Array.isArray(parsed)) {
          console.log(
            "111",
            parsed.map((record) => this.normalizeRecord(record))
          );
          return parsed.map((record) => this.normalizeRecord(record));
        }
        // 如果是单个对象
        if (typeof parsed === "object" && parsed !== null) {
          return [this.normalizeRecord(parsed)];
        }
      } catch (e) {
        // 如果不是JSON格式，尝试其他解析方式
        console.warn("处理记录数据格式解析失败:", e);
      }

      return [];
    },
  },
  mounted() {},
  methods: {
    goDetail() {
      this.$router.push({
        path: "/serve/workDispose",
        query: { id: this.info.gdId },
      });
    },
    close() {
      this.$emit("close");
    },
    // 标准化记录数据，确保包含所有必要字段
    normalizeRecord(record) {
      // 如果是字符串，尝试解析为对象
      if (typeof record === "string") {
        try {
          record = JSON.parse(record);
        } catch (e) {
          // 如果解析失败，创建一个基本对象
          return {
            name: record,
            operationDetail: "",
            type: "",
            assigneeUserName: "",
            assigneeCompanyName: "",
            assigneeRoleName: "",
            gmtCreate: "",
            glaimTime: "",
            approveResult: "",
            approveAnnex: "",
            approveOpinion: "",
            reason: "",
            accountabilityReport: "",
          };
        }
      }

      // 确保对象包含所有必要字段
      const normalizedRecord = {
        name: record.name || record.taskName || "",
        operationDetail: record.operationDetail || record.description || "",
        type: record.type || "",
        assigneeUserName: record.assigneeUserName || record.userName || "",
        assigneeCompanyName:
          record.assigneeCompanyName || record.companyName || "",
        assigneeRoleName: record.assigneeRoleName || record.roleName || "",
        gmtCreate: record.gmtCreate || record.createTime || "",
        glaimTime: record.glaimTime || record.completeTime || "",
        approveResult: record.approveResult || record.result || "",
        approveAnnex: this.normalizeApproveAnnex(
          record.approveAnnex || record.annex
        ),
        approveOpinion: record.approveOpinion || record.opinion || "",
        reason: record.reason || "",
        accountabilityReport:
          record.accountabilityReport || record.report || "",
      };

      return normalizedRecord;
    },
    // 标准化审批附件数组
    normalizeApproveAnnex(annexData) {
      if (!annexData) {
        return [];
      }

      // 如果已经是数组
      if (Array.isArray(annexData)) {
        return annexData
          .map((item) => {
            if (typeof item === "string") {
              // 如果数组项是字符串，尝试解析为JSON
              try {
                const parsed = JSON.parse(item);
                return {
                  name: parsed.name || parsed.fileName || "附件",
                  url: parsed.url || parsed.fileUrl || item,
                };
              } catch (e) {
                // 如果解析失败，假设字符串就是URL
                return {
                  name: "附件",
                  url: item,
                };
              }
            } else if (typeof item === "object" && item !== null) {
              // 如果是对象，标准化字段名
              return {
                name: item.name || item.fileName || item.title || "附件",
                url: item.url || item.fileUrl || item.path || "",
              };
            }
            return null;
          })
          .filter((item) => item && item.url);
      }

      // 如果是字符串，尝试解析
      if (typeof annexData === "string") {
        try {
          const parsed = JSON.parse(annexData);
          if (Array.isArray(parsed)) {
            return this.normalizeApproveAnnex(parsed);
          } else if (typeof parsed === "object" && parsed !== null) {
            return [
              {
                name: parsed.name || parsed.fileName || "附件",
                url: parsed.url || parsed.fileUrl || annexData,
              },
            ];
          }
        } catch (e) {
          // 如果解析失败，假设字符串就是URL
          return [
            {
              name: "附件",
              url: annexData,
            },
          ];
        }
      }

      return [];
    },
    // 下载文件
    downloadFile(fileUrl, fileName) {
      if (!fileUrl) {
        this.$message.warning("文件地址不存在");
        return;
      }

      try {
        // 创建一个临时的a标签来触发下载
        const link = document.createElement("a");
        link.style.display = "none";

        // 如果是完整的URL，直接使用
        if (fileUrl.startsWith("http")) {
          link.href = fileUrl;
        } else {
          // 如果是相对路径，需要拼接基础URL
          const baseUrl = process.env.VUE_APP_BASE_API || "";
          link.href = baseUrl + fileUrl;
        }

        // 设置下载文件名
        if (fileName) {
          link.download = fileName;
        }

        // 添加到DOM，触发点击，然后移除
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$message.success("开始下载文件");
      } catch (error) {
        console.error("下载文件失败:", error);
        this.$message.error("下载文件失败");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 20px;
  box-sizing: border-box;
  .line {
    display: table;
    width: 100%;
    table-layout: fixed;
    &:last-child {
      border-bottom: 1px solid #eff0f1;
    }
    .label {
      display: table-cell;
      width: 200px;
      background: #f9fafb;
      border: 1px solid #eff0f1;
      border-bottom: none;
      border-right: none;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      vertical-align: top;
      padding: 8px 21px;
    }
    .value {
      display: table-cell;
      vertical-align: top;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      border: 1px solid #eff0f1;
      border-bottom: none;
      text-align: left;
      box-sizing: border-box;
      padding: 8px 21px;
      background: #fff;
      word-break: break-all;

      // 处理记录表格样式
      .processing-records {
        margin-top: 8px;

        .el-table {
          font-size: 12px;

          .el-table__header {
            th {
              background-color: #f5f7fa;
              color: #606266;
              font-weight: 500;
            }
          }

          .el-table__body {
            tr:hover > td {
              background-color: #f5f7fa;
            }
          }

          .el-button--text {
            color: #409eff;
            padding: 0;
            font-size: 12px;

            &:hover {
              color: #66b1ff;
            }
          }
        }

        // 附件按钮样式
        .annex-buttons {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .annex-btn {
            text-align: left;
            justify-content: flex-start;
            padding: 2px 4px;
            margin: 0;
            min-height: auto;
            line-height: 1.2;

            &:hover {
              background-color: #ecf5ff;
              border-radius: 2px;
            }
          }
        }
      }
    }
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}
</style>
