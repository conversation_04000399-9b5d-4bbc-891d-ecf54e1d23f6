<template>
    <div>
        <el-table :data="tableData" border>
            <el-table-column type="index" label="序号" align="center">
            </el-table-column>
            <el-table-column prop="filePath" label="文件类型" align="center">
                <template slot-scope="scope">
                    <div class="file_type">
                        <img :src="getFileIcon(getFileTypeByUrl(scope.row.filePath))" :alt="scope.row.fileName" />
                        <span class="icon-info">{{ getExtensionFromUrl(scope.row.filePath) }}</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="fileName" label="文件名称" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <!-- <el-button type="text" @click="handleUpLoad(scope.$index, scope.row)">上传</el-button> -->
                    <el-button type="text" @click="handlePreview(scope.$index, scope.row)">预览</el-button>
                    <el-button type="text" @click="handleDownload(scope.$index, scope.row)">下载</el-button>
                </template>
            </el-table-column>

        </el-table>
    </div>
</template>

<script>

export default {
    props: {
        tableData: {
            type: Array,
            default: (() => {
                return []
            })
        },
    },
    data() {
        return {

        }
    },
    methods: {
        handlePreview(index, row) {
            window.open(row.filePath, '_blank');
        },
        handleDownload(index, row) {
            const a = document.createElement('a');
            a.href = row.filePath;
            a.target = '_blank'; 
            a.rel = 'noopener noreferrer';
            a.style.display = 'none';

            document.body.appendChild(a);
            a.click();

            document.body.removeChild(a);

        },
        getFileTypeByUrl(url) {
            const extension = this.getExtensionFromUrl(url);
            const typeMap = {
                'doc': 1,
                'docx': 1,
                'xls': 2,
                'xlsx': 2,
                'xlsm': 2,
                'xlsb': 2,
                'pdf': 3
            };
            return typeMap[extension] || 0;
        },
        getExtensionFromUrl(url) {
            if (typeof url !== 'string' || url.trim() === '') return '';
            const filenameMatch = url.match(/[^/?#]+$/);
            if (!filenameMatch) return '';
            const filename = filenameMatch[0];
            const lastDotIndex = filename.lastIndexOf('.');
            if (lastDotIndex === -1 || lastDotIndex === 0 || lastDotIndex === filename.length - 1) {
                return '';
            }
            return filename.slice(lastDotIndex + 1).toLowerCase();
        },
        getFileIcon(type) {
            const iconMap = {
                1: require("@/assets/images/serve/word.png"), // 文档
                2: require("@/assets/images/serve/excel.png"), // 表格
                3: require("@/assets/images/serve/pdf.png"), // PDF
                4: "", // 图片类型不使用图标，直接显示缩略图
            };

            return iconMap[type] || require("@/assets/images/serve/word.png");
        },
    }
}


</script>

<style lang="scss">
.icon-info {
    font-size: 22px;
    margin-left: 10px;
    width: 55px;
}

.file_type {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>