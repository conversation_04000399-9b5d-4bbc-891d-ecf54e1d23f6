<template>
  <el-dialog title="基础设施告警" :visible.sync="showFlag" width="100%" top="0" @close="close">
    <div style="width: 100%; height: calc(100vh - 180px)">
      <iframe
        :src="src"
        frameborder="no"
        style="width: 100%; height: 100%"
        scrolling="auto"
      />
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    src: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showFlag: false,
    };
  },
  watch: {
    show: function (val) {
      this.showFlag = val;
    },
  },
  computed: {},
  mounted() {},
  methods: {
    close() {
      this.showFlag = false;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
