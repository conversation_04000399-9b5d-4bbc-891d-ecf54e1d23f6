<template>
  <div class="container">
    <splitpanes class="default-theme">
      <!--部门数据-->
      <pane size="16" class="left-pane">
        <div class="left-content">
          <div class="head-container">
            <el-input
              v-model="deptName"
              placeholder="请输入部门名称"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
            />
            <el-tree
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              node-key="id"
              :default-expanded-keys="defaultExpandedKeys"
              highlight-current
              @node-click="handleNodeClick"
            />
          </div>
        </div>
      </pane>
      <!--用户数据-->
      <pane size="84" class="right-pane">
        <div class="right-content">
          <div class="card">
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              label-width="100px"
            >
              <el-form-item label="应用名称" prop="name">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入"
                  clearable
                  style="width: 180px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="系统地址" prop="systemUrl">
                <el-input
                  v-model="queryParams.systemUrl"
                  placeholder="请输入"
                  clearable
                  style="width: 180px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="关联IP" prop="glip">
                <el-input
                  v-model="queryParams.glip"
                  placeholder="请输入"
                  clearable
                  style="width: 180px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="运维厂商" prop="ywcs">
                <el-input
                  v-model="queryParams.ywcs"
                  placeholder="请输入"
                  clearable
                  style="width: 180px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleQuery"
                >
                  查询
                </el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </div>
          <div class="card flex-b" style="padding: 0 30px 0 0">
            <div class="total">
              已找到<span class="blue"> {{ total }} </span>个web业务系统
            </div>
            <div>
              <el-button
                type="success"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
              >
                新增
              </el-button>
              <el-button
                type="info"
                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                >导入</el-button
              >
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-loading="loading" class="loading-container" v-if="loading">
            <div style="height: 200px"></div>
          </div>

          <!-- 数据列表 -->
          <div v-if="!loading">
            <div class="card dataItem" v-for="(item, i) in datalist" :key="i">
              <div class="flex-b">
                <div class="flex-c">
                  <div class="name" @click="handleDetail(item)">
                    {{ item.xtmc }}
                  </div>
                </div>
                <div class="statusTag flex-c-c">{{ item.xtStatus }}</div>
              </div>
              <div class="flex-b">
                <div style="width: 500px">
                  <div class="line flex-c">
                    <img src="@/assets/images/monitor/net.png" class="icon" />
                    <span class="text cut_text"> {{ item.systemUrl }}</span>
                  </div>
                  <div class="line flex-c">
                    <img
                      src="@/assets/images/monitor/building.png"
                      class="icon"
                    />
                    <span class="text"> {{ item.deptName }}</span>
                  </div>
                </div>
                <div class="infoCon">
                  <div class="infoBox">
                    <div class="alignText">
                      <img
                        src="@/assets/images/monitor/dbgd.png"
                        class="icon2"
                      />
                      <div>待办工单</div>
                    </div>
                    <div
                      class="text2 text_red"
                      v-if="item.dbgd"
                      @click="goOrder(item)"
                    >
                      {{ item.dbgd }}
                    </div>
                    <div class="text2" v-else>暂无</div>
                  </div>
                  <div class="infoBox">
                    <div class="alignText">
                      <img
                        src="@/assets/images/monitor/jcss.png"
                        class="icon2"
                      />
                      <div>待处置告警</div>
                    </div>
                    <div
                      class="text2 text_red"
                      v-if="item.dczgj"
                      @click="goWarning(item)"
                    >
                      {{ item.dczgj }}
                    </div>
                    <div class="text2" v-else>暂无</div>
                  </div>
                  <div class="infoBox">
                    <div class="alignText">
                      <img
                        src="@/assets/images/monitor/ddjc.png"
                        class="icon2"
                      />
                      <div>基础设施检测</div>
                    </div>
                    <div class="text3" @click="lookWarning(item)">查看</div>
                  </div>
                </div>

                <div class="right flex-c">
                  <img
                    v-if="!item.inCollection"
                    src="@/assets/images/monitor/star.png"
                    class="icon3"
                    @click="handleCollect(item, 1)"
                  />
                  <i
                    v-else
                    class="el-icon-star-on"
                    style="color: #2563eb; font-size: 22px"
                    @click="handleCollect(item, 0)"
                  ></i>
                  <el-popover
                    ref="popover"
                    placement="bottom-end"
                    trigger="click"
                    popper-class="myPopover"
                    @show="item.showPopver = true"
                    @hide="item.showPopver = false"
                  >
                    <i
                      class="el-icon-more moreBtn"
                      slot="reference"
                      :style="{
                        color: item.showPopver ? '#0057FE' : '#9CA3AF',
                      }"
                    ></i>
                    <div class="popoverList">
                      <div
                        class="pItem flex-c-c"
                        v-for="(x, j) in popoverList"
                        :key="j"
                        @click="handleApp(item, j)"
                      >
                        <img :src="x.icon" class="icon" />
                        <div class="name">{{ x.name }}</div>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </div>
          <div
            class="card"
            style="display: flex; justify-content: flex-end; margin-top: 20px"
          >
            <el-pagination
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              :current-page="queryParams.pageNum"
              :page-size="queryParams.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </pane>
    </splitpanes>
    <!-- 基础设施告警弹框 -->
    <jcssDialog
      :show="warningDialogVisible"
      :src="warningSrc"
      @close="warningDialogVisible = false"
    />
    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <!-- 上传区域 -->
      <div>
        <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :on-error="handleFileError"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </el-upload>
      </div>

      <!-- 导入结果显示区域 -->
      <div v-if="upload.showResult" class="import-result-container">
        <!-- 导入成功 -->
        <div
          v-if="upload.resultType === 'success'"
          class="import-result success"
        >
          <div class="result-header">
            <span class="result-label">导入结果</span>
            <span class="result-status success-text">导入成功</span>
          </div>
        </div>

        <!-- 导入失败 -->
        <div v-if="upload.resultType === 'error'" class="import-result error">
          <div class="result-header">
            <span class="result-label">导入结果</span>
            <span class="result-status error-text">导入失败</span>
            <!-- <el-button
              type="text"
              size="small"
              class="download-error-btn"
              @click="downloadErrorFile"
            >
              失败原因下载
            </el-button> -->
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!upload.showResult"
          type="primary"
          @click="submitFileForm"
          :loading="upload.isUploading"
        >
          {{ upload.isUploading ? "上传中..." : "确 定" }}
        </el-button>
        <el-button @click="closeImportDialog">
          {{ upload.showResult ? "关 闭" : "取 消" }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listApplication,
  delApplication,
  downloadApplicationTemplate,
} from "@/api/property/applicationManage";
import { deptTreeSelect } from "@/api/serve/orderlist";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import jcssDialog from "@/views/monitorCenter/application/components/jcssDialog";
import { getYdjcymtz } from "@/api/login";
import cache from "@/plugins/cache";
import { getToken } from "@/utils/auth";

export default {
  name: "ApplicationManage",
  components: { Splitpanes, Pane, jcssDialog },
  data() {
    return {
      // 页面缓存键名
      cacheKey: "applicationManage_pageState",
      // 部门名称
      deptName: undefined,
      // 所有部门树选项
      deptOptions: undefined,
      // 过滤掉已禁用部门树选项
      enabledDeptOptions: undefined,
      // 默认展开的节点keys（只展开第一层）
      defaultExpandedKeys: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        glip: undefined,
        systemUrl: undefined,
        ywcs: undefined,
        constructionUnitId: undefined,
      },
      // 页面状态
      loading: false,
      tabList: ["系统卡片", "系统缩略图", "系统管理"],
      tabIndex: 0,
      total: 0,
      // 应用列表数据
      datalist: [],
      popoverList: [
        { name: "档案", icon: require("@/assets/images/property/file.png") },
        { name: "编辑", icon: require("@/assets/images/property/edit.png") },
        { name: "删除", icon: require("@/assets/images/property/delete.png") },
      ],
      //基础设施告警弹框
      warningDialogVisible: false,
      warningSrc: "",
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/tyywpt/yyDl/importData",
        // 是否显示导入结果
        showResult: false,
        // 导入结果类型：success/error
        resultType: "",
        // 导入结果消息
        resultMessage: "",
        // 错误文件下载地址
        errorFileUrl: "",
      },
    };
  },
  computed: {
    // 计算头部高度
    headerHeight() {
      // 基础头部高度 50px (navbar)
      let height = 50;
      // 如果启用了 tagsView，需要加上 34px
      if (this.$store.state.settings.tagsView) {
        height += 34;
      }
      // 加上 breadcrumb 高度 50px
      height += 50;
      return height;
    },
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    // 先获取部门树数据，然后恢复页面状态
    this.getDeptTree().then(() => {
      this.restorePageState();
      this.getList();
    });
  },
  mounted() {
    // 设置CSS变量
    document.documentElement.style.setProperty(
      "--header-height",
      this.headerHeight + "px"
    );
  },
  // 组件激活时（从其他页面返回时）
  activated() {
    // 检查是否需要恢复状态
    const cachedState = cache.session.getJSON(this.cacheKey);
    if (cachedState && cachedState.timestamp) {
      // 如果缓存时间在5分钟内，则恢复状态
      const now = Date.now();
      if (now - cachedState.timestamp < 5 * 60 * 1000) {
        // 确保树数据已加载，然后恢复状态
        if (this.deptOptions) {
          this.restorePageState();
          this.getList();
        } else {
          // 如果树数据未加载，先加载树数据再恢复状态
          this.getDeptTree().then(() => {
            this.restorePageState();
            this.getList();
          });
        }
      }
    }
  },
  // 组件失活时（跳转到其他页面时）
  deactivated() {
    this.savePageState();
  },
  // 组件销毁前
  beforeDestroy() {
    this.savePageState();
  },
  methods: {
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "应用导入";
      this.upload.open = true;
      this.upload.showResult = false;
      this.upload.resultType = "";
      this.upload.resultMessage = "";
      this.upload.errorFileUrl = "";
    },
    /** 下载模板操作 */
    importTemplate() {
      downloadApplicationTemplate({ id: 1 }).then((res) => {
        if (res.data.url) {
          location.href = res.data.url;
        }
      });
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      // 显示导入结果
      this.upload.showResult = true;

      if (response.code === 200) {
        // 导入成功
        this.upload.resultType = "success";
        this.upload.resultMessage = response.msg || "导入成功";
        this.getList(); // 刷新列表
      } else {
        // 导入失败
        this.upload.resultType = "error";
        this.upload.resultMessage = response.msg || "导入失败";
        // 如果有错误文件下载地址，保存它
        if (response.errorFileUrl) {
          this.upload.errorFileUrl = response.errorFileUrl;
        }
      }
    },
    // 文件上传失败处理
    handleFileError(error) {
      this.upload.isUploading = false;
      this.upload.showResult = true;
      this.upload.resultType = "error";
      this.upload.resultMessage = "上传失败，请重试";
      this.$refs.upload.clearFiles();
    },
    // 关闭导入对话框
    closeImportDialog() {
      this.upload.open = false;
      this.upload.showResult = false;
      this.upload.resultType = "";
      this.upload.resultMessage = "";
      this.upload.errorFileUrl = "";
    },
    // 下载错误文件
    downloadErrorFile() {
      if (this.upload.errorFileUrl) {
        // 如果有具体的错误文件地址，下载该文件
        window.open(this.upload.errorFileUrl, "_blank");
      } else {
        // 否则生成一个包含错误信息的文件
        this.$message.error("没有错误文件，请检查数据！");
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 保存页面状态到缓存
    savePageState() {
      const state = {
        queryParams: { ...this.queryParams },
        deptName: this.deptName,
        tabIndex: this.tabIndex,
        // 保存树的当前选中节点
        currentNodeKey: this.$refs.tree ? this.$refs.tree.getCurrentKey() : null,
        // 保存树的展开节点keys
        expandedKeys: this.$refs.tree ? this.$refs.tree.getExpandedKeys() : [],
        timestamp: Date.now(),
      };
      cache.session.setJSON(this.cacheKey, state);
    },

    // 从缓存恢复页面状态
    restorePageState() {
      const cachedState = cache.session.getJSON(this.cacheKey);
      if (cachedState) {
        // 恢复查询参数
        if (cachedState.queryParams) {
          this.queryParams = {
            ...this.queryParams,
            ...cachedState.queryParams,
          };
        }
        // 恢复部门名称
        if (cachedState.deptName !== undefined) {
          this.deptName = cachedState.deptName;
        }
        // 恢复标签页索引
        if (cachedState.tabIndex !== undefined) {
          this.tabIndex = cachedState.tabIndex;
        }

        // 恢复树状图状态需要在下一个tick中执行，确保DOM已渲染
        this.$nextTick(() => {
          if (this.$refs.tree) {
            // 恢复树的展开状态
            if (cachedState.expandedKeys && cachedState.expandedKeys.length > 0) {
              // 先设置展开的节点
              this.$refs.tree.setExpandedKeys(cachedState.expandedKeys);
            }

            // 恢复树的选中状态和高亮
            if (cachedState.currentNodeKey) {
              // 设置当前选中节点并高亮
              this.$refs.tree.setCurrentKey(cachedState.currentNodeKey);
              // 确保选中的节点可见（如果需要的话，展开其父节点）
              const node = this.$refs.tree.getNode(cachedState.currentNodeKey);
              if (node && node.parent) {
                // 展开父节点路径
                let parent = node.parent;
                const keysToExpand = [];
                while (parent && parent.key !== undefined) {
                  keysToExpand.push(parent.key);
                  parent = parent.parent;
                }
                if (keysToExpand.length > 0) {
                  this.$refs.tree.setExpandedKeys([...cachedState.expandedKeys || [], ...keysToExpand]);
                }
              }
            }
          }
        });
      }
    },

    // 清除页面状态缓存
    clearPageState() {
      cache.session.remove(this.cacheKey);
    },

    goOrder(item) {
      // 跳转前保存当前状态
      this.savePageState();
      this.$router.push({
        path: "/serve/workorder",
        query: { status: 1, yyId: item.id },
      });
    },
    goWarning(item) {
      // 跳转前保存当前状态
      this.savePageState();
      this.$router.push({
        path: "/warning/warningList",
        query: { gjStatus: 1, yyId: item.id },
      });
    },
    lookWarning(item) {
      getYdjcymtz({ projectName: item.name }).then((res) => {
        if (res.code == 200) {
          this.warningSrc = res.msg;
          this.warningDialogVisible = true;
        }
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      return deptTreeSelect().then((response) => {
        this.deptOptions = response.data;
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
        // 设置默认展开第一层节点
        this.setDefaultExpandedKeys(response.data);
        return response;
      });
    },

    // 设置默认展开的节点（只展开第一层）
    setDefaultExpandedKeys(treeData) {
      if (treeData && treeData.length > 0) {
        // 根据图片效果，只展开根节点（全华市），显示其直接子部门
        this.defaultExpandedKeys = treeData.length > 0 ? [treeData[0].id] : [];
      }
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      console.log(data, "1111111");
      this.queryParams.constructionUnitId = data.id;
      this.handleQuery();
      // 节点选择后保存状态，确保选中状态能被缓存
      this.$nextTick(() => {
        this.savePageState();
      });
    },
    changeTab(i) {
      this.tabIndex = i;
      // 切换标签页后保存状态
      this.savePageState();
    },
    // 获取应用列表
    async getList() {
      try {
        this.loading = true;
        console.log("查询参数:", this.queryParams);

        const response = await listApplication(this.queryParams);
        console.log("API响应:", response);

        if (response.code === 200 && response.data) {
          // 处理返回的数据，添加前端需要的字段
          this.datalist = (response.data.list || []).map((item) => ({
            ...item,
            // 添加前端展示需要的字段
            inCollection: false,
            showPopver: false,
            // 映射字段名称
            xtmc: item.name || item.xtmc,
            dwmc: item.constructionUnitName || item.dwmc,
            url: item.domain || item.url || item.xtym,
            xtStatus: this.getStatusText(item.xtStatus),
          }));

          this.total = response.data.total || 0;
          console.log("处理后的数据列表:", this.datalist);
        } else {
          this.datalist = [];
          this.total = 0;
          console.error("获取应用列表失败:", response.msg || response.message);
          this.$message.error(response.msg || "获取应用列表失败");
        }
      } catch (error) {
        console.error("获取应用列表异常:", error);
        this.datalist = [];
        this.total = 0;
        this.$message.error("获取应用列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "谋划中",
        2: "建设中",
        3: "试运行",
        4: "运行中",
        5: "停用",
      };
      return statusMap[status] || "未知";
    },

    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      // 查询后保存状态
      this.savePageState();
    },

    // 重置按钮操作
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        glip: undefined,
        systemUrl: undefined,
        ywcs: undefined,
        constructionUnitId: undefined,
      };
      // 重置树的选中状态
      if (this.$refs.tree) {
        this.$refs.tree.setCurrentKey(null);
      }
      this.getList();
      // 重置后保存状态
      this.savePageState();
    },

    // 新增按钮操作
    handleAdd() {
      // 跳转前保存当前状态
      this.savePageState();
      // 跳转到应用新增页面
      this.$router.push({
        path: "/property/applicationManageEdit",
        query: {
          mode: "add",
        },
      });
    },

    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },

    // 分页 - 每页条数变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleCollect(item, type) {
      if (type == 0) {
        item.inCollection = false;
      } else {
        item.inCollection = true;
      }
    },
    handleApp(item, type) {
      if (type == 0) {
        //档案
        // 跳转前保存当前状态
        this.savePageState();
        this.$router.push({
          path: "/monitorCenter/applicationDetail",
          query: { id: item.id },
        });
      } else if (type == 1) {
        //编辑 - 跳转到编辑页面
        // 跳转前保存当前状态
        this.savePageState();
        this.$router.push({
          path: "applicationManageEdit",
          query: {
            id: item.id,
            formData: item,
          },
        });
      } else {
        //删除
        this.$modal
          .confirm("确定要删除这个应用吗？")
          .then(function () {
            return delApplication(item.id);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {});
      }
    },
    handleDetail(item) {
      // 跳转前保存当前状态
      this.savePageState();
      this.$router.push({
        path: "/monitorCenter/applicationDetail",
        query: { id: item.id },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: calc(100vh - var(--header-height, 50px));
  overflow: hidden;
  background-color: #f5f5f5;
}

// 左右面板等高样式
:deep(.splitpanes) {
  height: 100%;
}

:deep(.splitpanes__pane) {
  height: 100%;
}

:deep(.splitpanes__splitter) {
  background-color: #e4e7ed;
  position: relative;
  z-index: 1;
}

:deep(.splitpanes__splitter:hover) {
  background-color: #c0c4cc;
}

.left-pane {
  background-color: #fff;
  height: 100%;
  overflow: hidden;
}

.left-content {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
}

.right-pane {
  height: 100%;
  overflow: hidden;
}
.splitpanes.default-theme .right-pane {
  background-color: #f2f4f7 !important;
}

.right-content {
  height: 100%;
  overflow-y: auto;
  padding: 0 20px;
  padding-bottom: 40px;
  box-sizing: border-box;
  background-color: #f2f4f7;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
}
.card {
  ::v-deep .el-form-item {
    margin-bottom: 0 !important;
  }
}
.dataItem {
  margin-bottom: 12px !important;
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    cursor: pointer;
  }
  .tagList {
    margin-left: 12px;
    .tag {
      padding: 4px 10px;
      box-sizing: border-box;
      margin-right: 8px;
      background-color: #eff6ff;
      border-radius: 4px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #2563eb;
      line-height: 16px;
      text-align: left;
    }
  }
  .statusTag {
    border-radius: 20px;
    padding: 4px 12px;
    box-sizing: border-box;
    background-color: #f0fdf4;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #16a34a;
    line-height: 20px;
    text-align: left;
  }
  .line {
    margin-top: 12px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
    .icon {
      width: 14px;
      height: 15px;
      margin-right: 6px;
    }
  }
  .infoCon {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex: 1;
    margin-right: 68px;
    .infoBox {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #9ca3af;
      line-height: 20px;
      text-align: center;
      display: flex;
      align-items: center;
      // width: 300px;
      min-width: 180px;
      margin-left: 20px;
      .alignText {
        text-align: center;
        margin-right: 55px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #4e5969;
        line-height: 24px;
        .icon2 {
          width: 40px;
          height: 40px;
          margin-bottom: 10px;
        }
      }
      .text2 {
        margin-top: 10px;
      }
      .text3 {
        width: 53px;
        height: 24px;
        background: #eff6ff;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #0057fe;
        font-size: 14px;
        color: #0057fe;
        line-height: 22px;
        text-align: center;
        cursor: pointer;
      }
      .text_red {
        color: orangered;
        font-size: 20px;
        cursor: pointer;
      }
    }
  }
  .right {
    width: 50px;
    .icon3 {
      width: 22px;
      height: 28px;
      cursor: pointer;
    }
    .moreBtn {
      cursor: pointer;
      transform: rotate(90deg);
      // color: #9ca3af;
      font-size: 22px;
      margin-left: 12px;
    }
  }
}
.popoverList {
  width: fit-content;

  .pItem {
    width: fit-content;
    border-bottom: solid 1px #d8d8d8;
    padding: 10px 18px;
    box-sizing: border-box;
    cursor: pointer;
    .icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .name {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #4e5969;
      line-height: 22px;
      text-align: left;
    }
  }
}
</style>
<style lang="scss" scoped>
.el-popover {
  padding: 0;
  min-width: unset;
}
.tabList {
  padding: 0 20px;
  box-sizing: border-box;
  .tab {
    margin-right: 56px;
    padding: 20px 0;
    box-sizing: border-box;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
    cursor: pointer;
  }
  .tab_active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 700;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    border-bottom: solid 3px #0057fe;
  }
}
.total {
  padding: 20px 30px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  .blue {
    color: #0057fe;
  }
}
.head-container {
  height: 100%;
}

// 覆盖AppMain的样式，确保当前页面不会滚动
.app-main {
  height: calc(100vh - 1px);
  margin: 0 !important;
}

// 隐藏其他可能影响布局的组件
.iframe-toggle,
.copyright {
  display: none !important;
}

// 导入结果样式
.import-result-container {
  margin-bottom: 20px;
  margin-top: 24px;
}

.import-result {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;

  &.success {
    background-color: #f0f9ff;
    border: 1px solid #e0f2fe;
  }

  &.error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
  }
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .result-label {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
  }

  .result-status {
    font-size: 14px;
    font-weight: 500;

    &.success-text {
      color: #16a34a;
    }

    &.error-text {
      color: #dc2626;
    }
  }

  .download-error-btn {
    color: #2563eb;
    font-size: 12px;
    padding: 4px 8px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    background-color: #fff;

    &:hover {
      background-color: #f9fafb;
      border-color: #2563eb;
    }
  }
}
</style>
