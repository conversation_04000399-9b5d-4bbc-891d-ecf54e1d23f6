<template>
  <div class="container">
    <!-- 查询条件卡片 -->
    <div class="card">
      <div class="cardTitle">供应商管理</div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
        <el-form-item label="供应商名称" prop="gysName">
          <el-input
            v-model="queryParams.gysName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="统一信用代码" prop="tyxydm">
          <el-input
            v-model="queryParams.tyxydm"
            placeholder="请输入统一信用代码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="简介" prop="introduce">
          <el-input
            v-model="queryParams.introduce"
            placeholder="请输入简介关键词"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮卡片 -->
    <div class="card">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 数据表格卡片 -->
    <div class="card">
      <el-table v-loading="loading" :data="supplierList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="供应商名称" align="center" prop="gysName" />
        <el-table-column label="统一信用代码" align="center" prop="tyxydm" />
        <el-table-column label="地址" align="center" prop="address" />
        <el-table-column label="邮编" align="center" prop="yb" />
        <el-table-column label="法定代表人姓名" align="center" prop="fddbrxm" />
        <el-table-column label="简介" align="center" prop="introduce" show-overflow-tooltip />
        <el-table-column label="创建时间" align="center" prop="cTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.cTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改供应商对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="供应商名称" prop="gysName">
          <el-input v-model="form.gysName" placeholder="请输入供应商名称" />
        </el-form-item>
        <el-form-item label="统一信用代码" prop="tyxydm">
          <el-input v-model="form.tyxydm" placeholder="请输入统一信用代码" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="邮编" prop="yb">
          <el-input v-model="form.yb" placeholder="请输入邮编" />
        </el-form-item>
        <el-form-item label="法定代表人姓名" prop="fddbrxm">
          <el-input v-model="form.fddbrxm" placeholder="请输入法定代表人姓名" />
        </el-form-item>
        <el-form-item label="简介" prop="introduce">
          <el-input
            v-model="form.introduce"
            type="textarea"
            :rows="3"
            placeholder="请输入供应商简介"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSupplier,
  getSupplier,
  delSupplier,
  addSupplier,
  updateSupplier
} from "@/api/property/supplierManage";

export default {
  name: "SupplierManage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商表格数据
      supplierList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        gysName: null,
        tyxydm: null,
        introduce: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        gysName: [
          { required: true, message: "供应商名称不能为空", trigger: "blur" }
        ],
        tyxydm: [
          { required: true, message: "统一信用代码不能为空", trigger: "blur" },
          { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: "请输入正确的统一信用代码", trigger: "blur" }
        ],
        introduce: [
          { max: 500, message: "简介不能超过500个字符", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询供应商列表 */
    getList() {
      this.loading = true;
      listSupplier(this.queryParams).then(response => {
        this.supplierList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        gysName: null,
        tyxydm: null,
        address: null,
        yb: null,
        fddbrxm: null,
        introduce: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // 过滤掉无效的id
      this.ids = selection.map(item => item.id).filter(id => id != null && id !== undefined);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加供应商";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let id;

      if (row && row.id) {
        // 从行操作按钮点击，直接使用行数据的id
        id = row.id;
      } else if (this.ids && this.ids.length === 1) {
        // 从工具栏按钮点击，使用选中的第一个id
        id = this.ids[0];
      } else {
        // 没有选中数据或选中多条数据
        this.$message.error('请选择一条数据进行修改');
        return;
      }

      if (!id) {
        this.$message.error('无效的数据ID');
        return;
      }

      getSupplier(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改供应商";
      }).catch(error => {
        console.error('获取供应商数据失败:', error);
        this.$message.error('获取数据失败');
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 只提交后端支持的字段
          const submitData = {
            id: this.form.id,
            gysName: this.form.gysName,
            tyxydm: this.form.tyxydm,
            address: this.form.address,
            yb: this.form.yb,
            fddbrxm: this.form.fddbrxm,
            introduce: this.form.introduce,
          };

          if (this.form.id != null) {
            updateSupplier(submitData).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSupplier(submitData).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let ids;

      if (row && row.id) {
        // 从行操作按钮点击，删除单条数据
        ids = [row.id];
      } else if (this.ids && this.ids.length > 0) {
        // 从工具栏按钮点击，删除选中的数据
        ids = this.ids;
      } else {
        this.$message.error('请选择要删除的数据');
        return;
      }

      // 过滤掉无效的id
      const validIds = ids.filter(id => id != null && id !== undefined);
      if (validIds.length === 0) {
        this.$message.error('无效的数据ID');
        return;
      }

      const idsStr = validIds.join(',');
      this.$modal.confirm('是否确认删除供应商？').then(() => {
        return delSupplier(idsStr);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch((error) => {
        console.error('删除失败:', error);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tyywpt/tTyywGys/export', {
        ...this.queryParams
      }, `supplier_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}

.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;

  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>
