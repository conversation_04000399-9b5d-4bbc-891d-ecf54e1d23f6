<template>
  <div class="container">
    <!-- 查询条件卡片 -->
    <div class="card">
      <div class="cardTitle">运维人员管理</div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="80px"
      >
        <el-form-item label="账户" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入账户"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="人员分类" prop="ryfl">
          <el-select
            v-model="queryParams.ryfl"
            placeholder="请选择人员分类"
            clearable
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="昵称" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入昵称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属厂商" prop="sscs">
          <el-select
            v-model="queryParams.sscs"
            placeholder="输入检索供应商"
            clearable
            filterable
            remote
            :remote-method="remoteSearchSuppliers"
            :loading="supplierLoading"
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职位" prop="zw">
          <el-input
            v-model="queryParams.zw"
            placeholder="请输入职位"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="考核认证" prop="sfkhrz">
          <el-select
            v-model="queryParams.sfkhrz"
            placeholder="请选择考核认证"
            clearable
          >
            <el-option
              v-for="item in certificationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮卡片 -->
    <div class="card">
      <el-row :gutter="10" class="mb8">
        <!--        <el-col :span="1.5">-->
        <!--          <el-button-->
        <!--            type="primary"-->
        <!--            plain-->
        <!--            icon="el-icon-plus"-->
        <!--            size="mini"-->
        <!--            @click="handleAdd"-->
        <!--          >新增</el-button>-->
        <!--        </el-col>-->
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            >修改</el-button
          >
        </el-col>
        <!--        <el-col :span="1.5">-->
        <!--          <el-button-->
        <!--            type="danger"-->
        <!--            plain-->
        <!--            icon="el-icon-delete"-->
        <!--            size="mini"-->
        <!--            :disabled="multiple"-->
        <!--            @click="handleDelete"-->
        <!--          >删除</el-button>-->
        <!--        </el-col>-->
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
    </div>

    <!-- 数据表格卡片 -->
    <div class="card">
      <el-table
        v-loading="loading"
        :data="personnelList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="账户" align="center" prop="userName" />
        <el-table-column prop="nickName" label="昵称" align="center">
          <template slot-scope="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                >{{ scope.row.nickName
                }}<!-- 显示状态：睁眼图标 --></span
              >
              <!-- <i
                v-if="nameVisibilityMap[scope.row.id]"
                class="el-icon-view"
                style="
                  margin-left: 4px;
                  cursor: pointer;
                  color: #409eff;
                  font-size: 12px;
                "
                @click="toggleNameVisibility(scope.row.id)"
                title="隐藏姓名"
              ></i>
              <i
                v-else
                class="custom-eye-hide"
                style="
                  margin-left: 4px;
                  cursor: pointer;
                  color: #409eff;
                  font-size: 12px;
                "
                @click="toggleNameVisibility(scope.row.id)"
                title="显示姓名"
              ></i> -->
            </div>
          </template></el-table-column
        >
        <el-table-column prop="phone" label="联系电话" align="center">
          <template slot-scope="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                >{{ formatPhone(scope.row.phone, scope.row.id)
                }}<!-- 显示状态：睁眼图标 --></span
              ><i
                v-if="phoneVisibilityMap[scope.row.id]"
                class="el-icon-view"
                style="margin-left: 8px; cursor: pointer; color: #409eff"
                @click="togglePhoneVisibility(scope.row.id)"
                title="隐藏手机号"
              ></i>
              <!-- 隐藏状态：闭眼图标 -->
              <i
                v-else
                class="custom-eye-hide"
                style="margin-left: 8px; cursor: pointer; color: #409eff"
                @click="togglePhoneVisibility(scope.row.id)"
                title="显示手机号"
              ></i>
            </div> </template
        ></el-table-column>
        <el-table-column label="人员分类" align="center" prop="ryfl">
          <template slot-scope="scope">
            <span>{{ getCategoryName(scope.row.ryfl) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="职位" align="center" prop="zw" />
        <el-table-column label="办公电话" align="center" prop="bgdh" />
        <el-table-column label="邮箱" align="center" prop="email" />
        <el-table-column label="所属厂商" align="center" prop="gysName" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="考核认证" align="center" prop="sfkhrz">
          <template slot-scope="scope">
            <span>{{ getCertificationName(scope.row.sfkhrz) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="保密协议" align="center" prop="sfyqsbmxy">
          <template slot-scope="scope">
            <el-tag :type="scope.row.sfyqsbmxy === 1 ? 'success' : 'info'">
              {{ scope.row.sfyqsbmxy === 1 ? "已签署" : "未签署" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="cTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.cTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <!--            <el-button-->
            <!--              size="mini"-->
            <!--              type="text"-->
            <!--              icon="el-icon-delete"-->
            <!--              @click="handleDelete(scope.row)"-->
            <!--            >删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改运维人员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账户" prop="userName">
              <el-input
                v-model="form.userName"
                placeholder="请输入账户"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickName">
              <el-input
                v-model="form.nickName"
                placeholder="请输入昵称"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人员分类" prop="ryfl">
              <el-select
                v-model="form.ryfl"
                placeholder="请选择人员分类"
                style="width: 100%"
              >
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="办公电话" prop="bgdh">
              <el-input
                v-model="form.bgdh"
                placeholder="请输入办公电话"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="职位" prop="zw">
                <el-input v-model="form.zw" placeholder="请输入职位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属厂商" prop="sscs">
                <el-select
                  v-model="form.sscs"
                  placeholder="输入检索供应商"
                  style="width: 100%"
                  clearable
                  filterable
                  remote
                  :remote-method="remoteSearchSuppliers"
                  :loading="supplierLoading"
                >
                  <el-option
                    v-for="item in supplierOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="form.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核认证" prop="sfkhrz">
              <el-select
                v-model="form.sfkhrz"
                placeholder="请选择考核认证"
                style="width: 100%"
              >
                <el-option
                  v-for="item in certificationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="保密协议" prop="sfyqsbmxy">
          <el-radio-group v-model="form.sfyqsbmxy">
            <el-radio :label="1">已签署</el-radio>
            <el-radio :label="0">未签署</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMaintenancePersonnel,
  getMaintenancePersonnel,
  delMaintenancePersonnel,
  saveMaintenancePersonnel,
  getSuppliers,
  searchSuppliers,
} from "@/api/property/maintenancePersonnel";

export default {
  name: "MaintenancePersonnelManage",
  data() {
    return {
      // 手机号码显示状态控制
      phoneVisibilityMap: {},
      // 姓名显示状态控制
      nameVisibilityMap: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 运维人员表格数据
      personnelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        nickName: null,
        phone: null,
        bgdh: null,
        email: null,
        ryfl: null,
        zw: null,
        sscs: null,
        status: null,
        sfkhrz: null,
        sfyqsbmxy: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" },
        ],
        userName: [
          { required: true, message: "账户不能为空", trigger: "blur" },
        ],
        nickName: [
          { required: true, message: "昵称不能为空", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        email: [
          { type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] },
        ],
        ryfl: [
          { required: true, message: "人员分类不能为空", trigger: "change" },
        ],
        zw: [{ required: true, message: "职位不能为空", trigger: "blur" }],
        sscs: [
          { required: true, message: "所属厂商不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
      },
      // 人员分类选项
      categoryOptions: [
        { label: "一线", value: 1 },
        { label: "二线", value: 2 },
        { label: "三线", value: 3 },
      ],
      // 状态选项
      statusOptions: [
        { label: "正常", value: 1 },
        { label: "停用", value: 2 },
      ],
      // 认证选项
      certificationOptions: [
        { label: "已通过", value: 1 },
        { label: "未通过", value: 0 },
      ],
      // 供应商选项
      supplierOptions: [],
      // 供应商加载状态
      supplierLoading: false,
      // 搜索防抖定时器
      searchTimer: null,
    };
  },
  created() {
    this.getList();
    this.getSupplierOptions();
    // 初始化显示状态（默认隐藏）
    this.initVisibility();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },
  methods: {
    // 初始化显示状态
    initVisibility() {
      const phoneVisibilityMap = {};
      const nameVisibilityMap = {};
      this.noticeList.forEach((item) => {
        phoneVisibilityMap[item.id] = false; // 默认隐藏手机号
        nameVisibilityMap[item.id] = false; // 默认隐藏姓名
      });
      this.phoneVisibilityMap = phoneVisibilityMap;
      this.nameVisibilityMap = nameVisibilityMap;
    },
    // 格式化姓名显示（脱敏处理）
    formatName(name, id) {
      if (!name) return "";

      // 根据姓名显示状态决定是否脱敏
      if (this.nameVisibilityMap[id]) {
        return name; // 显示完整姓名
      } else {
        // 姓名脱敏处理
        if (name.length === 1) {
          return name; // 单字姓名不脱敏
        } else if (name.length === 2) {
          return name.charAt(0) + "*"; // 两字姓名：张*
        } else if (name.length === 3) {
          return name.charAt(0) + "*" + name.charAt(2); // 三字姓名：张*三
        } else {
          // 四字及以上姓名：保留首尾，中间用*代替
          return (
            name.charAt(0) +
            "*".repeat(name.length - 2) +
            name.charAt(name.length - 1)
          );
        }
      }
    },
    // 格式化手机号码显示
    formatPhone(phone, id) {
      if (!phone) return "";

      // 检查是否为有效的手机号码格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return phone; // 如果不是标准手机号格式，直接返回原值
      }

      // 根据显示状态决定是否隐藏中间四位
      if (this.phoneVisibilityMap[id]) {
        return phone; // 显示完整手机号
      } else {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2"); // 隐藏中间四位
      }
    },
    // 切换姓名显示状态
    toggleNameVisibility(id) {
      this.$set(this.nameVisibilityMap, id, !this.nameVisibilityMap[id]);
    },
    // 切换手机号码显示状态
    togglePhoneVisibility(id) {
      this.$set(this.phoneVisibilityMap, id, !this.phoneVisibilityMap[id]);
    },
    /** 查询运维人员列表 */
    getList() {
      this.loading = true;
      listMaintenancePersonnel(this.queryParams)
        .then((response) => {
          this.personnelList = response.data.list;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /** 获取供应商选项 */
    getSupplierOptions() {
      this.supplierLoading = true;
      getSuppliers()
        .then((response) => {
          // 将供应商列表转换为下拉选项格式
          this.supplierOptions = response.data.list.map((item) => ({
            label: item.gysName,
            value: item.id,
          }));
          this.supplierLoading = false;
        })
        .catch(() => {
          // 如果获取失败，使用默认选项
          this.supplierOptions = [];
          this.supplierLoading = false;
        });
    },
    /** 远程搜索供应商 */
    remoteSearchSuppliers(query) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置防抖，300ms后执行搜索
      this.searchTimer = setTimeout(() => {
        if (query !== "") {
          this.supplierLoading = true;
          searchSuppliers(query)
            .then((response) => {
              // 将搜索结果转换为下拉选项格式
              this.supplierOptions = response.data.list.map((item) => ({
                label: item.sscs,
                value: item.sscs,
              }));
              this.supplierLoading = false;
            })
            .catch(() => {
              this.supplierOptions = [];
              this.supplierLoading = false;
            });
        } else {
          // 如果查询为空，重新加载所有供应商
          this.getSupplierOptions();
        }
      }, 300);
    },
    /** 获取人员分类名称 */
    getCategoryName(category) {
      const categoryItem = this.categoryOptions.find(
        (item) => item.value === category
      );
      return categoryItem ? categoryItem.label : category;
    },
    /** 获取状态名称 */
    getStatusName(status) {
      const statusItem = this.statusOptions.find(
        (item) => item.value === status
      );
      return statusItem ? statusItem.label : status;
    },
    /** 获取认证名称 */
    getCertificationName(certification) {
      const certificationItem = this.certificationOptions.find(
        (item) => item.value === certification
      );
      return certificationItem ? certificationItem.label : certification;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        userName: null,
        nickName: null,
        phone: null,
        bgdh: null,
        email: null,
        ryfl: null,
        zw: null,
        sscs: null,
        status: 1,
        sfkhrz: 0,
        sfyqsbmxy: 0,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.userId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加运维人员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.userId || this.ids[0];
      getMaintenancePersonnel(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改运维人员";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          saveMaintenancePersonnel(this.form).then(() => {
            this.$modal.msgSuccess(this.form.id ? "修改成功" : "新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除运维人员编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMaintenancePersonnel(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "tyywpt/tTyywYyry/export",
        {
          ...this.queryParams,
        },
        `maintenancePersonnel_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}

.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;

  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>
