<template>
  <div class="container">
    <div class="tabCon">
      <Tab2
        :tablist="tablist"
        :tabIndex="tabIndex"
        @changeTab="changeTab"
      ></Tab2>
    </div>
    <div class="tabCon-placeholder" v-if="isFixed"></div>
    <div class="card flex-b top">
      <div class="left flex-c">
        <div class="name">工单编号：{{ data.gdNo }}</div>
        <div class="clzt flex-c-c">{{ formatStatus(data.status) }}</div>
      </div>
      <div class="right">
        <el-button
          size="small"
          @click="showRedeploy"
          v-if="anQx.zp == 1 && (data.status == 1 || data.status == 2)"
          >转派</el-button
        >
        <el-button
          type="success"
          size="small"
          @click="showSolve"
          v-if="anQx.jj == 1 && (data.status == 1 || data.status == 2)"
          >标记为已解决</el-button
        >
        <el-button
          @click="showClose"
          size="small"
          v-if="anQx.gbgd == 1 && (data.status == 1 || data.status == 2)"
          >关闭工单</el-button
        >
        <el-button
          size="small"
          @click="showEvaluate"
          v-if="anQx.pj == 1 && data.status == 3"
          >评价工单</el-button
        >
        <el-button size="small" @click="showRead" v-if="anQx.cs == 1"
          >已阅</el-button
        >
      </div>
    </div>
    <div class="card" id="baseInfo">
      <div class="cardTitle">基本信息</div>
      <baseInfo :data="data"></baseInfo>
    </div>
    <div class="card" id="description">
      <div class="cardTitle">工单描述</div>
      <description :data="data"></description>
    </div>
    <!-- <div class="card" id="disposition">
      <div class="cardTitle">工单处理</div>
      <disposition :data="data"></disposition>
    </div> -->
    <div class="card" id="disposeRecord">
      <div class="flex-c">
        <div class="cardTitle">处理记录</div>
        <!-- <el-button
          size="small"
          icon="el-icon-plus"
          style="margin: 0 0 10px 20px"
        >
          添加处理记录
        </el-button> -->
      </div>
      <disposeRecord
        v-if="gdId"
        :gdId="gdId"
        :loadFlag="loadFlag"
      ></disposeRecord>
    </div>
    <!-- <div class="card" id="disposeContent">
      <div class="cardTitle">处理内容</div>
      <el-input
        type="textarea"
        :rows="4"
        placeholder="输入处理内容..."
        v-model="data.clnr"
      >
      </el-input>
    </div>
    <div class="card" id="appendix">
      <div class="cardTitle">附件</div>
      <FileUpload :limit="1" v-model="fj"></FileUpload>
    </div> -->
    <!-- <div class="card">
      <div class="footer">
        <el-button type="primary" @click="submit"> 保存记录 </el-button>
        <el-button @click="goBack">取消</el-button>
      </div>
    </div> -->

    <!-- 转派 -->
    <el-dialog
      :visible.sync="show"
      title="转派"
      width="400px"
      :close-on-click-modal="false"
    >
      <div style="display: flex; justify-content: center">
        <el-form
          :model="form"
          :rules="rules"
          ref="form"
          label-position="left"
          label-width="100px"
        >
          <el-form-item label="处理人" prop="handlerId">
            <el-select v-model="form.handlerId" style="width: 100%">
              <el-option
                v-for="(item, i) in clrOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <div class="footer">
            <el-button type="primary" @click="submit0"> 确 定 </el-button>
            <el-button @click="cancel0"> 取 消 </el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <!-- 标记为已解决 -->
    <el-dialog
      :visible.sync="show1"
      title="标记为已解决"
      width="400px"
      :close-on-click-modal="false"
    >
      <div style="display: flex; justify-content: center">
        <el-form
          :model="form1"
          :rules="rules1"
          ref="form1"
          label-position="left"
          label-width="100px"
        >
          <el-form-item label="解决方式" prop="content">
            <el-input
              type="textarea"
              :rows="2"
              v-model="form1.content"
              placeholder="请输入"
            />
          </el-form-item>
          <div class="footer">
            <el-button type="primary" @click="submit1"> 确 定 </el-button>
            <el-button @click="cancel1"> 取 消 </el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <!-- 关闭工单 -->
    <el-dialog
      :visible.sync="show2"
      title="关闭工单"
      width="400px"
      :close-on-click-modal="false"
    >
      <div style="display: flex; justify-content: center">
        <el-form
          :model="form2"
          :rules="rules2"
          ref="form2"
          label-position="left"
          label-width="100px"
        >
          <el-form-item label="关闭原因" prop="content">
            <el-input
              type="textarea"
              :rows="2"
              v-model="form2.content"
              placeholder="请输入"
            />
          </el-form-item>
          <div class="footer">
            <el-button type="primary" @click="submit2"> 确 定 </el-button>
            <el-button @click="cancel2"> 取 消 </el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <!-- 评价工单 -->
    <el-dialog
      :visible.sync="show3"
      title="评价工单"
      width="400px"
      :close-on-click-modal="false"
    >
      <div style="display: flex; justify-content: center">
        <el-form
          :model="form3"
          :rules="rules3"
          ref="form3"
          label-position="left"
          label-width="100px"
        >
          <el-form-item label="评价" prop="satisfaction">
            <el-rate v-model="form3.satisfaction"></el-rate>
          </el-form-item>
          <el-form-item label="描述" prop="content">
            <el-input
              type="textarea"
              :rows="2"
              v-model="form3.content"
              placeholder="请输入"
            />
          </el-form-item>
          <div class="footer">
            <el-button type="primary" @click="submit3"> 确 定 </el-button>
            <el-button @click="cancel3"> 取 消 </el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Tab2 from "@/views/serve/components/Tab2.vue";
import baseInfo from "./baseInfo.vue";
import description from "./description.vue";
import disposition from "./disposition.vue";
import disposeRecord from "./disposeRecord.vue";
import FileUpload from "@/components/FileUpload/index.vue";
import { getGdInfo, listClry, handleGd, getAnzy } from "@/api/serve/orderlist";

export default {
  components: {
    Tab2,
    baseInfo,
    description,
    disposition,
    disposeRecord,
    FileUpload,
  },
  data() {
    return {
      gdId: "",
      isFixed: false,
      tabIndex: 0,
      statusOptions: [
        { label: "一线待处理", value: 1 },
        { label: "二线待处理", value: 2 },
        { label: "已解决", value: 3 },
        { label: "已复核", value: 4 },
        { label: "已评价", value: 5 },
        { label: "已关闭", value: 6 },
      ],
      tablist: [
        { name: "基本信息", href: "baseInfo" },
        { name: "工单描述", href: "description" },
        // { name: "工单处理", href: "disposition" },
        { name: "处理记录", href: "disposeRecord" },
        // { name: "处理内容", href: "disposeContent" },
        // { name: "附件", href: "appendix" },
      ],
      data: {},
      anQx: {},
      fj: [],
      loadFlag: false,
      //转派
      show: false,
      clrOptions: [],
      form: { handlerId: "" },
      rules: {
        handlerId: [{ required: true, message: "请选择", trigger: "change" }],
      },
      //标记为已解决
      show1: false,
      form1: { content: "" },
      rules1: {
        content: [{ required: true, message: "输入", trigger: "blur" }],
      },
      //关闭工单
      show2: false,
      form2: { content: "" },
      rules2: {
        content: [{ required: true, message: "输入", trigger: "blur" }],
      },
      //评价工单
      show3: false,
      form3: { satisfaction: 0, content: "" },
      rules3: {
        satisfaction: [
          { required: true, message: "请选择", trigger: "change" },
        ],
        content: [{ required: true, message: "输入", trigger: "blur" }],
      },
    };
  },
  computed: {
    userId() {
      return this.$store.state.user.id;
    },
  },
  mounted() {
    window.addEventListener("scroll", this.handleScroll);
    this.init();
    const gdId = this.$route.query.id;
    console.log("Parent component - route query id:", gdId);
    console.log("Parent component - route query:", this.$route.query);
    this.gdId = gdId;
    console.log("Parent component - gdId set to:", this.gdId);
    if (this.gdId) {
      this.getDetails();
      getAnzy({ id: this.gdId }).then((res) => {
        this.anQx = res.data;
      });
    }
  },

  methods: {
    init() {
      listClry().then((res) => {
        this.clrOptions = res.data.map((item) => {
          return {
            label: item.userName,
            value: item.userId,
          };
        });
      });
    },
    //已阅
    showRead() {
      handleGd({
        gdId: this.gdId,
        type: 6,
      }).then((res) => {
        this.getDetails();
        getAnzy({ id: this.gdId }).then((res) => {
          this.anQx = res.data;
        });
        this.$message.success("操作成功");
      });
    },
    //转派
    showRedeploy() {
      this.show = true;
    },
    submit0() {
      handleGd({
        gdId: this.gdId,
        handlerId: this.form.handlerId,
        type: 1,
      }).then((res) => {
        this.getDetails();
        this.loadFlag = !this.loadFlag;
        this.$message.success("转派成功");
        this.show = false;
      });
    },
    cancel0() {
      this.show = false;
    },
    //标记为已解决
    showSolve() {
      this.show1 = true;
    },
    submit1() {
      handleGd({
        gdId: this.gdId,
        content: this.form1.content,
        type: 2,
      }).then((res) => {
        this.getDetails();
        this.loadFlag = !this.loadFlag;
        this.$message.success("标记为已解决成功");
        this.show1 = false;
      });
    },
    cancel1() {
      this.show1 = false;
    },
    //关闭工单
    showClose() {
      this.show2 = true;
    },
    submit2() {
      handleGd({
        gdId: this.gdId,
        content: this.form2.content,
        type: 4,
      }).then((res) => {
        this.getDetails();
        this.loadFlag = !this.loadFlag;
        this.$message.success("关闭工单成功");
        this.show2 = false;
      });
    },
    cancel2() {
      this.show2 = false;
    },
    //评价工单
    showEvaluate() {
      this.show3 = true;
    },
    submit3() {
      handleGd({
        gdId: this.gdId,
        satisfaction: this.form3.satisfaction,
        content: this.form3.content,
        type: 5,
      }).then((res) => {
        this.getDetails();
        this.loadFlag = !this.loadFlag;
        this.$message.success("评价工单成功");
        this.show3 = false;
      });
    },
    cancel3() {
      this.show3 = false;
    },
    formatStatus(i) {
      let res = "";
      this.statusOptions.forEach(function (item) {
        if (item.value === i) {
          res = item.label;
        }
      });
      return res;
    },
    getDetails() {
      getGdInfo({ id: this.gdId }).then((res) => {
        this.data = res.data;
      });
    },
    submit() {
      console.log("保存");
    },
    changeTab(e) {
      this.tabIndex = e.index;
      const targetId = e.href;
      const target = document.getElementById(targetId);

      if (target) {
        const offset = 12;
        const targetPosition = target.offsetTop - offset;

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });

        // 更新URL hash（不触发路由跳转）
        history.replaceState(null, null, `#${targetId}`);
      }
    },
    handleScroll() {
      const tabCon = document.querySelector(".tabCon");
      if (window.scrollY > 100) {
        tabCon.classList.add("fixed");
        this.isFixed = true;
      } else {
        tabCon.classList.remove("fixed");
        this.isFixed = false;
      }
    },
    goBack() {
      this.$router.go(-1);
    },
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll);
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 60px;
  box-sizing: border-box;
  position: relative;
  .tabCon {
    position: relative;
    z-index: 10;
    // padding: 0 20px;
    // box-sizing: border-box;
  }
  .tabCon.fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding: 0 20px;
    box-sizing: border-box;
  }
  .tabCon-placeholder {
    width: 100%;
    height: 107px;
  }
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-top: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}

.top {
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-left: 16px;
  }
  .clzt {
    padding: 4px 12px;
    box-sizing: border-box;
    border-radius: 40px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    background-color: #249eff;
    color: #fff;
    margin-left: 16px;
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>
