<template>
  <div class="wrap">
    <div class="list1" v-if="tabIndex == 0">
      <div class="card-grid">
        <div
          class="card-item"
          v-for="(item, index) in list1"
          :key="index"
          @click="handleClick(item)"
          :class="{ 'clickable': item.num > 0 }"
        >
          <div class="card-title">{{ item.name }}</div>
          <div class="card-number">
            <span class="number">{{ item.num || 0 }}</span>
            <span class="unit">个</span>
          </div>
        </div>
      </div>
    </div>
    <div class="list2" v-else>
      <div
        class="li flex-c"
        v-for="(item, i) in list2"
        :key="i"
        style="align-items: flex-start"
      >
        <div
          class="tag flex-c-c"
          :class="
            item.yxj == '紧急' ? 'tag_red' : item.yxj == '重要' ? 'tag_org' : ''
          "
        >
          {{ item.yxj }}
        </div>
        <div class="name cursor" @click="handleUpdate(item)">
          {{ item.noticeTitle }}
        </div>
        <div class="time">{{ parseTime(item.createTime, "{y}-{m}-{d}") }}</div>
      </div>
    </div>
    <!-- 查看 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="公告标题" prop="noticeTitle">
              <el-input
                disabled
                v-model="form.noticeTitle"
                placeholder="请输入公告标题"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="yxj">
              <el-select
                disabled
                v-model="form.yxj"
                placeholder="请选择紧急程度"
              >
                <el-option
                  v-for="dict in yxjOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-radio-group disabled v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_notice_status"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容">
              <editor readOnly v-model="form.noticeContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { listDbsxgd } from "@/api/home/<USER>";
import { listNotice, getNotice } from "@/api/system/notice";
export default {
  dicts: ["sys_notice_status"],
  props: {
    tabIndex: {
      type: Number,
      default: 0,
    },
  },
  mounted() {
    this.getList();
    this.getList1();
  },

  methods: {
    async getList() {
      const res = await listDbsxgd();
      this.list1 = res.data;
    },
    async getList1() {
      const res = await listNotice({ pageNum: 1, pageSize: 10 });
      this.list2 = res.rows;
    },
    handleClick(row) {
      // 只有当数量大于0时才允许点击跳转
      if (row.num && row.num > 0) {
        this.$router.push({
          path: "/serve/workorder",
          query: {
            gdType: row.dictValue,
          },
        });
      }
    },
    //公告弹框开始
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        yxj: undefined,
        noticeContent: undefined,
        status: "0",
      };
      this.resetForm("form");
    },
    handleUpdate(row) {
      this.reset();
      const noticeId = row.noticeId || this.ids;
      getNotice(noticeId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "查看公告";
      });
    },
  },

  data() {
    return {
      list1: [],
      list2: [],
      //公告弹框
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      yxjOptions: [
        { value: "紧急", label: "紧急" },
        { value: "重要", label: "重要" },
        { value: "常规", label: "常规" },
      ],
      form: {},
    };
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  height: inherit;
}
.list1 {
  width: 100%;
  height: 270px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none;
  }
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 10px 20px;

  .card-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 16px;
    width: 100%;
    max-width: 500px;
  }

  .card-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s ease;
    height: 72px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &.clickable {
      cursor: pointer;

      &:hover {
        background: #e3f2fd;
        border-color: #2196f3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
      }
    }

    .card-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #6c757d;
      line-height: 20px;
      margin-bottom: 8px;
    }

    .card-number {
      display: flex;
      align-items: baseline;
      justify-content: center;
      gap: 4px;

      .number {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        font-size: 28px;
        color: #1d2129;
        line-height: 1;
      }

      .unit {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #1d2129;
        line-height: 1;
      }
    }
  }
}
.list2 {
  width: 100%;
  height: 260px;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .li {
    margin-top: 20px;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
      width: 80%;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }
    .tag {
      padding: 1px 10px;
      box-sizing: border-box;
      background-color: #e8f3ff;
      font-size: 16px;
      color: #165dff;
      line-height: 26px;
      border-radius: 3px;
      margin-right: 12px;
      white-space: nowrap;
    }
    .tag_red {
      background-color: #ffecec;
      color: #ff0000;
    }
    .tag_org {
      background-color: #fff7e8;
      color: #ff7d00;
    }
    .time {
      white-space: nowrap;
      color: #8993a1;
      margin-top: 4px;
      margin-left: 10px;
    }
  }
}
::v-deep .el-table__cell {
  height: 36px !important;
  padding: 0;
}
</style>
