<template>
  <div class="container">
    <div class="card">
      <div class="flex-c">
        <div class="cardTitle">工单列表</div>
        <!-- <el-button
          type="primary"
          size="mini"
          style="margin: 0 0 8px 20px"
          @click="checkEvaluate"
          >综合评价</el-button
        > -->
      </div>

      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入标题"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="gdType">
          <el-select
            v-model="queryParams.gdType"
            placeholder="工单类型"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="dict in dict.type.gdlx"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="priority">
          <el-select
            v-model="queryParams.priority"
            placeholder="优先级"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in priorityOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="yyId">
          <el-select
            v-model="queryParams.yyId"
            placeholder="所属应用"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in appOptions"
              :key="i"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="deptId">
          <treeselect
            style="width: 160px; height: 32px"
            v-model="queryParams.deptId"
            :options="enabledDeptOptions"
            :show-count="true"
            placeholder="请选择归属部门"
          />
        </el-form-item>
        <el-form-item prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="处理状态"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in statusOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8" type="flex" justify="end">
        <!-- <el-col :span="1.5">
          <el-button size="small" @click="showDialog2"
            >工单发放设置管理</el-button
          >
        </el-col> -->
        <el-col :span="1.5">
          <el-button icon="el-icon-plus" size="small" @click="showDialog">
            新增
          </el-button>
        </el-col>
      </el-row>

      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="gdNo" label="工单编号" align="center" />
        <el-table-column
          prop="title"
          label="工单标题"
          align="center"
          min-width="140"
        />
        <el-table-column
          prop="gdType"
          label="工单类型"
          align="center"
          :formatter="formatGdType"
        />
        <el-table-column prop="" label="工单来源" align="center">
          <template slot-scope="scope">
            <div>
              {{ scope.row.cjType == 1 ? "手动新增" : "系统新增" }}
            </div></template
          >
        </el-table-column>
        <el-table-column
          prop="priority"
          label="优先级"
          align="center"
          :formatter="formatPriority"
        >
        </el-table-column>
        <el-table-column prop="yyName" label="所属应用" align="center" />
        <el-table-column prop="deptName" label="所属部门" align="center" />

        <el-table-column prop="createName" label="创建人" align="center"
          ><template slot-scope="scope">
            <div>
              {{ scope.row.createName ? scope.row.createName : "系统新增" }}
            </div>
          </template></el-table-column
        >
        <el-table-column prop="handlerName" label="处理人" align="center" />
        <el-table-column
          prop="status"
          label="处理状态"
          align="center"
          :formatter="formatStatus"
        >
        </el-table-column>
        <el-table-column prop="cTime" label="创建时间" align="center" />
        <el-table-column
          prop="resolutionTime"
          label="解决时间"
          align="center"
        />
        <el-table-column prop="satisfaction" label="满意度" align="center" />
        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleDetail(scope.$index, scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          layout="total, prev, pager, next"
          :total="total"
        ></el-pagination>
      </div>
    </div>

    <!-- 弹窗 -->
    <addOrderDialog
      :show="show"
      :info="info"
      @close="show = false"
      @addSuccess="addSuccess"
    ></addOrderDialog>

    <el-dialog :visible.sync="show2" title="系统设置" width="500px">
      <div style="display: flex; justify-content: center">
        <el-form
          :model="form2"
          ref="form2"
          label-position="top"
          label-width="100px"
          class="form2"
        >
          <div class="itemTitle">工单通知类型</div>
          <el-form-item prop="gdtzlx" class="checkboxGroup">
            <el-checkbox-group v-model="form2.gdtzlx">
              <el-checkbox label="启用钉钉通知"></el-checkbox>
              <el-checkbox label="启用邮件通知"></el-checkbox>
              <el-checkbox label="启用短信通知"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="itemTitle">自动分配规则</div>
          <el-form-item label="网络资源预警工单" prop="wlzyyjgd">
            <el-select
              v-model="form2.wlzyyjgd"
              multiple
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="(item, i) in personOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="安全预警工单" prop="aqyjgd">
            <el-select
              v-model="form2.aqyjgd"
              multiple
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="(item, i) in personOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <div class="footer">
            <el-button type="primary" @click="save" size="small">
              保存设置
            </el-button>
            <el-button @click="cancel2" size="small"> 取 消 </el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import addOrderDialog from "./addOrderDialog.vue";
import { listGd, listAllYy, deptTreeSelect } from "@/api/serve/orderlist";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  components: { Treeselect, addOrderDialog },
  dicts: ["gdlx"],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        gdType: undefined,
        priority: undefined,
        yyId: undefined,
        deptId: undefined,
        status: undefined,
      },
      priorityOptions: [
        { label: "低", value: 1 },
        { label: "中", value: 2 },
        { label: "高", value: 3 },
      ],
      appOptions: [],
      enabledDeptOptions: [],
      statusOptions: [
        { label: "未完成", value: 1 },
        // { label: "一线待处理", value: 1 },
        // { label: "二线待处理", value: 2 },
        { label: "已解决", value: 3 },
        // { label: "已复核", value: 4 },
        { label: "已评价", value: 5 },
        { label: "已关闭", value: 6 },
      ],
      statusOptionsStr: [
        { label: "一线待处理", value: 1 },
        { label: "二线待处理", value: 2 },
        { label: "已解决", value: 3 },
        // { label: "已复核", value: 4 },
        { label: "已评价", value: 5 },
        { label: "已关闭", value: 6 },
      ],
      loading: false,
      total: 1,
      tableData: [],
      //新增工单
      show: false,
      info: {},
      //弹窗2
      show2: false,
      form2: {
        gdtzlx: [],
        wlzyyjgd: [],
        aqyjgd: [],
      },
      personOptions: [],
    };
  },
  created() {
    if (this.$route.query.gdType) {
      this.queryParams.gdType = this.$route.query.gdType;
    }
    if (this.$route.query.yyId) {
      this.queryParams.yyId = Number(this.$route.query.yyId);
    }
    if (this.$route.query.status) {
      this.queryParams.status = Number(this.$route.query.status);
    }
    this.initData();
    this.getList();
  },
  methods: {
    initData() {
      listAllYy().then((res) => {
        this.appOptions = res.data;
      });
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
    },
    formatGdType(obj) {
      let res = "";
      this.dict.type.gdlx.forEach(function (item) {
        if (item.value == obj.gdType) {
          res = item.label;
        }
      });
      return res;
    },
    formatPriority(obj) {
      let res = "";
      this.priorityOptions.forEach(function (item) {
        if (item.value == obj.priority) {
          res = item.label;
        }
      });
      return res;
    },
    formatStatus(obj) {
      let res = "";
      this.statusOptionsStr.forEach(function (item) {
        if (item.value == obj.status) {
          res = item.label;
        }
      });
      return res;
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    getList() {
      this.loading = true;
      listGd(this.queryParams).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        gdType: undefined,
        priority: undefined,
        yyId: undefined,
        deptId: undefined,
        status: undefined,
      };
      this.getList();
    },
    //新增工单开始--
    showDialog() {
      this.show = true;
    },
    addSuccess() {
      this.show = false;
      this.getList();
    },
    //新增工单结束--
    //工单发放开始--
    showDialog2() {
      this.show2 = true;
    },
    save() {},
    cancel2() {
      this.show2 = false;
    },
    reset2() {
      this.form2 = {
        gdtzlx: [],
        wlzyyjgd: [],
        aqyjgd: [],
      };
      this.resetForm("form2");
    },
    //工单发放结束--
    handleDetail(index, row) {
      this.$router.push({ path: "/serve/workDispose", query: { id: row.id } });
    },
    checkEvaluate() {
      this.$router.push({ path: "/serve/evaluate" });
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
.card {
  ::v-deep .el-form-item {
    margin-bottom: 0 !important;
  }
}

.clzt_icon {
  padding-left: 16px;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 4px;
    left: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}
.clzt_blue {
  &::before {
    background-color: #249eff;
  }
}
.clzt_green {
  &::before {
    background-color: #86df6c;
  }
}
.clzt_dark {
  &::before {
    background-color: #0e42d2;
  }
}

// ::v-deep .el-form-item__label {
//   padding: 0 !important;
// }
.footer {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}
.itemTitle {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 700;
  font-size: 16px;
  color: #1d2129;
  line-height: 22px;
  text-align: left;
  margin-bottom: 12px;
}
.form2 {
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
  .el-checkbox {
    display: block;
  }
  ::v-deep .el-form-item__label {
    padding: 0 !important;
  }
}
::v-deep .vue-treeselect__control {
  height: 32px;
}
.required {
  ::v-deep .el-form-item__label::before {
    content: "*";
    color: #ff4949;
    margin-right: 4px;
  }
}
</style>
