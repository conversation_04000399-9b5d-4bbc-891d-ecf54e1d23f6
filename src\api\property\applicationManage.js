import request from "@/utils/request";

// 查询统一运维平台-应用列表
export function listApplication(query) {
  return request({
    url: "/tyywpt/tTyywYy/list",
    method: "get",
    params: query,
  });
}

// 获取统一运维平台-应用详细信息
export function getApplication(id) {
  return request({
    url: "/tyywpt/tTyywYy/getInfo",
    method: "get",
    params: { id: id },
  });
}

// 新增统一运维平台-应用
export function addApplication(data) {
  return request({
    url: "/tyywpt/tTyywYy/add",
    method: "post",
    data: data,
  });
}

// 修改统一运维平台-应用
export function updateApplication(data) {
  return request({
    url: "/tyywpt/tTyywYy/edit",
    method: "put",
    data: data,
  });
}

// 删除统一运维平台-应用
export function delApplication(ids) {
  return request({
    url: "/tyywpt/tTyywYy/remove",
    method: "delete",
    params: { ids: ids },
  });
}

// 导出统一运维平台-应用列表
export function exportApplication(query) {
  return request({
    url: "/tyywpt/tTyywYy/export",
    method: "post",
    params: query,
  });
}

// 获取所有应用列表
export function listAllApplication() {
  return request({
    url: "/tyywpt/tTyywYy/listAll",
    method: "get",
  });
}

// 下载应用导入模板
export function downloadApplicationTemplate(query) {
  return request({
    url: "/tyywpt/tMb/getInfo",
    method: "get",
    params: query,
  });
}

// 查询应用电信云资源列表
export function listYzyDetail(query) {
  return request({
    url: "/tyywpt/tTyywYyDxYzy/list",
    method: "get",
    params: query,
  });
}

// 通过名称查询资源列表
export function listYzyByNameForList(query) {
  return request({
    url: "/tyywpt/tTyywYyDxYzy/byNameForList",
    method: "get",
    params: query,
  });
}
