<template>
  <div style="width: 100%; height: 230px" id="zcbqfb"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  watch: {
    data: {
      handler(val) {
        this.initChart(val);
      },
      deep: true,
    },
  },
  methods: {
    initChart(data) {
      let total = 0;
      let resdata = data.map((x) => {
        return {
          name: x.name,
          value: x.num,
        };
      });
      data.forEach((x) => {
        total += parseFloat(x.num);
      });
      let chart = echarts.init(document.getElementById("zcbqfb"));
      let option = {
        color: [
          "#0E42D2",
          "#249EFF",
          "#21CCFF",
          "#86DF6C",
          "#846BCE",
          "#F98F1C",
        ],
        tooltip: {
          trigger: "item",
        },
        legend: {
          right: "10%",
          top: "center",
          icon: "circle",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 18,
          orient: "vertical",
          formatter: (name) => {
            let value = (
              (parseFloat(data.find((x) => x.name == name).num) / total) *
              100
            ).toFixed(2);
            return `{a|${name}}{b|${value}%}`;
          },
          textStyle: {
            padding: [0, 0, 0, 6],
            rich: {
              a: {
                width: 80,
              },
            },
          },
        },
        series: [
          {
            type: "pie",
            center: ["30%", "50%"],
            itemStyle: {
              borderWidth: 2,
              borderColor: "#fff",
            },
            label: {
              show: false,
              position: "outside", // 将标签放置在扇区外部
              formatter: "{d}%", // 标签内容格式化
            },
            labelLine: {
              show: false,
              length: 10, // 连接线长度
              length2: 10, // 引导线长度
            },
            data: resdata,
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
